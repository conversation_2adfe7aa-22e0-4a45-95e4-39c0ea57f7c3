<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>com.apple.security.network.client</key>
	<true/>
	<key>com.apple.security.network.server</key>
	<true/>
	<!-- <key>com.apple.security.app-sandbox</key>
	<true/> -->
	<key>com.apple.security.app-sandbox</key>
	<false/>
	<key>com.apple.security.files.user-selected.read-write</key>
	<true/>
	<key>com.apple.security.files.downloads.read-write</key>
	<true/>
	<key>com.apple.security.cs.allow-jit</key>
	<true/>
	<key>com.apple.security.network.server</key>
	<true/>
	<key>keychain-access-groups</key>
	<array>
		<string>$(AppIdentifierPrefix)com.ipman.app</string>
	</array>
		<!-- <key>com.apple.developer.system-extension.install</key>
		<true/> -->
<!-- 				
	<key>com.apple.developer.networking.networkextension</key>
	<array>
				<string>packet-tunnel-provider-systemextension</string>
				<string>app-proxy-provider-systemextension</string>
				<string>content-filter-provider-systemextension</string>
				<string>dns-proxy-systemextension</string> 
				<string>dns-settings</string> 
				<string>dns-proxy</string>
				<string>app-proxy-provider</string>
				<string>packet-tunnel-provider</string>
	</array>
	<key>com.apple.developer.networking.vpn.api</key>
	<string>allow-vpn</string> -->

</dict>
</plist>
